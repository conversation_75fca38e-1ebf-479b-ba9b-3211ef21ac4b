
export enum ListeningState {
  IDLE = "IDLE",
  LISTENING = "LISTENING",
  PROCESSING = "PROCESSING",
  ERROR = "ERROR",
  SUCCESS = "SUCCESS"
}

export interface VoiceCommand {
  type: string; // e.g., "NEW_TAB", "GO_TO_URL", "SWITCH_TAB"
  payload?: any; // e.g., { url: "https://google.com" } or { direction: "next" }
}

export interface ChromeMessage {
  type: "PROCESS_USER_COMMAND" | "STATUS_UPDATE" | "ERROR_OCCURRED";
  payload?: any;
}

export interface GroundingChunkWeb {
  uri: string;
  title: string;
}

export interface GroundingChunk {
 web: GroundingChunkWeb;
}