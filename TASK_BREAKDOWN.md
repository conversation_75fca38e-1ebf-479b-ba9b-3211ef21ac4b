
# Task Breakdown: Voice Navigator Chrome Extension

This document outlines the tasks required to develop the Voice Navigator Chrome Extension.

## Phase 1: Core Setup & UI

### 1.1. Project Initialization
-   [ ] Setup React with TypeScript project structure.
-   [ ] Configure Tailwind CSS (via CDN).
-   [ ] Create `manifest.json` for MV3 extension.
    -   [ ] Define basic properties (name, version, manifest_version).
    -   [ ] Configure action (popup HTML, default icons - placeholder paths).
    -   [ ] Setup background service worker entry.
    -   [ ] Add initial permissions (`tabs`).
-   [ ] Create basic `index.html` for popup.
-   [ ] Create `index.tsx` to render React app into `index.html`.
-   [ ] Create main `App.tsx` and `components/Popup.tsx`.

### 1.2. Popup UI Development
-   [ ] Design basic layout for `Popup.tsx` using Tailwind CSS.
-   [ ] Implement microphone button/icon (SVG).
-   [ ] Implement display area for status messages (listening, processing, error).
-   [ ] Implement display area for transcribed text (optional, for feedback).
-   [ ] Style UI elements for good aesthetics and UX.

### 1.3. Voice Recognition Setup (`services/voiceService.ts`)
-   [ ] Create `voiceService.ts`.
-   [ ] Implement `startListening()` and `stopListening()` methods using `window.webkitSpeechRecognition`.
-   [ ] Handle `onresult` to get transcript.
-   [ ] Handle `onerror`, `onstart`, `onend` events.
-   [ ] Connect `voiceService` to `Popup.tsx` state (update UI with transcript and status).
-   [ ] Handle microphone permissions prompt.

## Phase 2: Command Processing & Chrome Integration

### 2.1. Background Service Worker Setup (`background.ts`)
-   [ ] Create `background.ts`.
-   [ ] Setup message listener (`chrome.runtime.onMessage.addListener`) to receive commands from popup.

### 2.2. Command Parsing Logic (`services/commandService.ts` or integrated into `background.ts`)
-   [ ] Define data structures for commands.
-   [ ] Implement parsing for "Open new tab".
-   [ ] Implement parsing for "Close current tab".
-   [ ] Implement parsing for "Go to [website/URL]".
    -   [ ] Differentiate between direct URL and name for lookup.
-   [ ] Implement parsing for "Switch to next/previous tab".
-   [ ] Implement parsing for "Reload page", "Go back", "Go forward".

### 2.3. Chrome API Integration (in `background.ts`)
-   [ ] Execute "Open new tab" (`chrome.tabs.create`).
-   [ ] Execute "Close current tab" (`chrome.tabs.remove`).
-   [ ] Execute "Switch to next/previous tab" (logic with `chrome.tabs.query` and `chrome.tabs.update`).
-   [ ] Execute "Reload page" (`chrome.tabs.reload`).
-   [ ] Execute "Go back" (`chrome.tabs.goBack`).
-   [ ] Execute "Go forward" (`chrome.tabs.goForward`).
-   [ ] Implement navigation for direct URLs (`chrome.tabs.update` or `chrome.tabs.create`).

## Phase 3: Gemini API Integration

### 3.1. Gemini Service Setup (`services/geminiService.ts`)
-   [ ] Create `geminiService.ts`.
-   [ ] Initialize `GoogleGenAI` client using `process.env.API_KEY` (ensure this is handled correctly in service worker context).
-   [ ] Implement `findWebsiteUrl(name: string)` function.
    -   [ ] Construct prompt for Gemini (`gemini-2.5-flash-preview-04-17` model).
    -   [ ] Include system instruction to return only the URL.
    -   [ ] Make API call using `ai.models.generateContent`.
    -   [ ] Parse response to extract URL (`response.text`).
    -   [ ] Handle potential API errors.

### 3.2. Integrate Gemini Service with Background Script
-   [ ] In `background.ts`, when "Go to [website name]" is parsed:
    -   [ ] Call `geminiService.findWebsiteUrl()`.
    -   [ ] On successful URL retrieval, use `chrome.tabs.update` or `chrome.tabs.create` to navigate.
    -   [ ] Handle errors from `geminiService` and provide feedback to popup.

## Phase 4: Refinement & Polish

### 4.1. UI/UX Enhancements
-   [ ] Improve visual feedback for all states (listening, processing, success, error).
-   [ ] Animate microphone icon or provide clear visual cues.
-   [ ] Ensure popup is aesthetically pleasing and easy to use.
-   [ ] Consider auto-closing popup after successful command or providing an option.

### 4.2. Error Handling
-   [ ] Implement comprehensive error handling for all services (voice, command, Gemini, Chrome API).
-   [ ] Display user-friendly error messages in the popup.
-   [ ] Ensure graceful recovery from errors where possible.

### 4.3. Code Quality & Optimization
-   [ ] Review code for clarity, efficiency, and adherence to SOP.
-   [ ] Optimize performance of critical paths (voice recognition, command processing).
-   [ ] Ensure proper TypeScript typings are used throughout (`types.ts`).
-   [ ] Create and use shared constants (`constants.ts`).

### 4.4. Testing (Manual)
-   [ ] Test all voice commands thoroughly in various scenarios.
-   [ ] Test different website names for URL resolution with Gemini.
-   [ ] Test edge cases (no speech, unrecognized commands, API errors).
-   [ ] Test on different websites and browser states.

## Phase 5: Documentation & Packaging

### 5.1. Documentation Files
-   [x] Create PRD (`PRODUCT_REQUIREMENTS_DOCUMENT.md`).
-   [x] Create Technical Architecture document (`TECHNICAL_ARCHITECTURE.md`).
-   [x] Create Task Breakdown (`TASK_BREAKDOWN.md`).
-   [x] Create Coding SOP (`CODING_SOP.md`).

### 5.2. Extension Packaging
-   [ ] Add actual icons for the extension.
-   [ ] Prepare for packaging (ensure `background.ts` would be compiled to `background.js` in a real build setup).
-   [ ] Write clear usage instructions if needed (e.g., in a README).

## Future Enhancements (Post-MVP)
-   [ ] Implement keyboard shortcut for voice activation.
-   [ ] Add an options page for configuration (e.g., API key input if `process.env` is not viable, default behaviors).
-   [ ] Support for more complex commands or natural language understanding improvements.
-   [ ] Localization and multi-language support.
