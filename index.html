<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Voice Navigator</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body, html, #root {
      height: 100%;
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #1e293b; /* bg-slate-800 from Popup.tsx */
    }
    body {
      width: 400px;
      min-height: 380px; /* Matches min-h-[380px] in Popup.tsx */
      height: auto;
      overflow: hidden; /* Popups often don't scroll */
    }
    #root {
      display: flex;
      flex-direction: column;
    }
  </style>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.5.1"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body>
  <div id="root"></div>
  <script type="module" src="./index.tsx"></script>
<script type="module" src="/index.tsx"></script>
</body>
</html>
