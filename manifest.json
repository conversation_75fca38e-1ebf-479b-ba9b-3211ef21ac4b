{"manifest_version": 3, "name": "Voice Navigator", "description": "Control your browser with voice commands using React and Gemini.", "version": "1.0.0", "action": {"default_popup": "index.html", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "permissions": ["tabs", "scripting", "activeTab", "storage", "notifications"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self'; style-src 'self' 'unsafe-inline';"}}