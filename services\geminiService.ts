
import { GoogleGenAI, GenerateContentResponse, GroundingChunk } from "@google/genai";
import { GEMINI_MODEL_NAME, GEMINI_URL_SYSTEM_INSTRUCTION } from '../constants';

let ai: GoogleGenAI | null = null;

const getGenAIClient = (): GoogleGenAI => {
  if (!ai) {
    if (!process.env.API_KEY) {
      // This error will be thrown if API_KEY is not set, preventing API calls.
      // The background script has a console warning, but this service enforces the requirement.
      console.error("CRITICAL: API_KEY environment variable not set for Gemini. Gemini functionality will fail.");
      throw new Error("Gemini API Key not configured. Please ensure API_KEY environment variable is set.");
    }
    ai = new GoogleGenAI({ apiKey: process.env.API_KEY });
  }
  return ai;
};

export const findWebsiteUrl = async (websiteName: string): Promise<string | null> => {
  try {
    const client = getGenAIClient();
    const response: GenerateContentResponse = await client.models.generateContent({
      model: GEMINI_MODEL_NAME,
      contents: `What is the official website URL for "${websiteName}"?`,
      config: {
        systemInstruction: GEMINI_URL_SYSTEM_INSTRUCTION,
        temperature: 0.1, 
      },
    });

    let text = response.text.trim();
    if (text === 'URL_NOT_FOUND' || !text) {
      return null;
    }
    
    // Remove potential markdown like ``` from the response
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = text.match(fenceRegex);
    if (match && match[2]) {
      text = match[2].trim();
    }

    if (text.startsWith('http://') || text.startsWith('https://')) {
      return text;
    }
    // Simple check if it could be a domain name
    if(text.includes('.') && !text.includes(' ') && !text.includes('\n')) {
        // Check if it might be a path without protocol
        if(!text.startsWith('/')) {
             return `https://${text}`; 
        }
    }

    console.warn(`Gemini returned a non-URL for ${websiteName}: ${text}`);
    return null; 
  } catch (error) {
    console.error("Error fetching website URL from Gemini:", error);
    if (error instanceof Error) {
        throw new Error(`Gemini API error: ${error.message}`);
    }
    throw new Error("Unknown error occurred while contacting Gemini API.");
  }
};


export interface SearchResult {
  answer: string;
  sources: GroundingChunk[]; // This should be GroundingChunk as defined in types.ts or locally if specific
}

export const searchWithGoogle = async (query: string): Promise<SearchResult> => {
  try {
    const client = getGenAIClient();
    const response: GenerateContentResponse = await client.models.generateContent({
      model: GEMINI_MODEL_NAME, 
      contents: query,
      config: {
        tools: [{ googleSearch: {} }],
      },
    });

    const answer = response.text.trim();
    // Ensure groundingChunks are correctly accessed and typed
    const groundingMetadata = response.candidates?.[0]?.groundingMetadata;
    const sources: GroundingChunk[] = groundingMetadata?.groundingChunks || [];
    
    return { answer, sources };

  } catch (error) {
    console.error("Error performing Google Search with Gemini:", error);
    if (error instanceof Error) {
        throw new Error(`Gemini API (Google Search) error: ${error.message}`);
    }
    throw new Error("Unknown error occurred while performing Google Search via Gemini API.");
  }
};
