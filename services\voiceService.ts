
import { useState, useCallback, useRef, useEffect } from 'react';

// Attempt to get the SpeechRecognition object
const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;

export const useSpeechRecognition = (
  onResult: (transcript: string) => void,
  onError: (error: string) => void,
  onStart?: () => void,
  onEnd?: () => void
) => {
  const [isListening, setIsListening] = useState(false);
  const recognitionRef = useRef<any>(null); // Using 'any' for SpeechRecognition instance

  useEffect(() => {
    if (!SpeechRecognition) {
      // Defer error callback to allow component to mount, then call onError.
      // This is safer than calling it directly during render.
      setTimeout(() => onError("Speech Recognition API is not supported in this browser."), 0);
      return;
    }

    const recognition = new SpeechRecognition();
    recognition.continuous = false; // Process single utterances
    recognition.interimResults = false; // We only want final results
    recognition.lang = 'en-US';

    recognition.onstart = () => {
      setIsListening(true);
      if (onStart) onStart();
    };

    recognition.onresult = (event: any) => { // Using 'any' for SpeechRecognitionEvent
      let transcript = '';
      for (let i = event.resultIndex; i < event.results.length; ++i) {
        if (event.results[i].isFinal) {
          transcript += event.results[i][0].transcript;
        }
      }
      if (transcript) {
        onResult(transcript.trim());
      }
    };

    recognition.onerror = (event: any) => { // Using 'any' for SpeechRecognitionErrorEvent
      console.log("Speech recognition error:", event.error, event);
      let errorMsg = event.error;
      if (event.error === 'no-speech') {
        errorMsg = "No speech detected. Please try speaking again.";
      } else if (event.error === 'audio-capture') {
        errorMsg = "Audio capture failed. Please check your microphone connection.";
      } else if (event.error === 'not-allowed') {
        errorMsg = "Microphone access denied. Please click the microphone icon in your browser's address bar and allow access, then reload the extension.";
      } else if (event.error === 'network') {
        errorMsg = "Network error during speech recognition. Please check your internet connection.";
      } else if (event.error === 'service-not-allowed') {
        errorMsg = "Speech recognition service not allowed. Please check your browser settings and ensure microphone access is enabled.";
      } else if (event.error === 'aborted') {
        // Don't treat aborted as an error - user likely stopped intentionally
        setIsListening(false);
        if (onEnd) onEnd();
        return;
      } else {
        errorMsg = `Speech recognition error: ${event.error}`;
      }
      onError(errorMsg);
      setIsListening(false);
      if (onEnd) onEnd();
    };

    recognition.onend = () => {
      setIsListening(false);
      if (onEnd) onEnd();
    };

    recognitionRef.current = recognition;

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.abort();
      }
    };
  }, [onResult, onError, onStart, onEnd]); // Effect re-runs if any callback identity changes.

  const startListening = useCallback(() => {
    console.log("startListening called, isListening:", isListening, "recognitionRef.current:", recognitionRef.current);
    if (recognitionRef.current && !isListening) {
      try {
        console.log("Starting speech recognition...");
        recognitionRef.current.start();
      } catch (e: any) {
        console.error("Error starting speech recognition:", e);
        onError("Could not start voice recognition: " + e.message);
        setIsListening(false); // Ensure state consistency
        if (onEnd) onEnd(); // Call onEnd if start fails and it effectively ends recognition attempt
      }
    } else {
      console.log("Cannot start listening - either already listening or no recognition object");
    }
  }, [isListening, onError, onEnd]); // Dependencies include external callbacks if they affect logic here

  const stopListening = useCallback(() => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
      // onend will be called, which sets isListening to false and calls props.onEnd
    }
  }, [isListening]); // Only depends on internal state

  return { isListening, startListening, stopListening };
};
