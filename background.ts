// This declares the 'chrome' extension API global for TypeScript
// In a full project, you would install @types/chrome instead.
declare const chrome: any;

import { COMMANDS } from './constants';
import { findWebsiteUrl, searchWithGoogle } from './services/geminiService';
import { ChromeMessage, GroundingChunk } from './types'; 

// Ensure this file is treated as a module.
export {};

console.log("Voice Navigator Background Service Worker Started.");

// Helper to send messages to the active tab's popup, if open.
const sendPopupMessage = (message: ChromeMessage) => {
  if (chrome && chrome.runtime && chrome.runtime.sendMessage) {
    chrome.runtime.sendMessage(message).catch((error: Error) => {
      if (error.message && error.message.includes("Receiving end does not exist")) {
        // This is expected if the popup is not open.
      } else {
        console.warn("Error sending message to popup:", error.message);
      }
    });
  } else {
    console.warn("Cannot send message to popup: chrome.runtime.sendMessage not available in background.");
  }
};


const isValidUrl = (string: string): boolean => {
  if (!string) return false;
  try {
    // Basic check first, URL constructor can be too lenient with local paths etc.
    if (!string.startsWith('http://') && !string.startsWith('https://')) {
        return false;
    }
    new URL(string);
    return true;
  } catch (_) {
    return false;  
  }
};

if (chrome && chrome.runtime && chrome.runtime.onMessage) {
  chrome.runtime.onMessage.addListener((message: ChromeMessage, sender: any, sendResponse: (response?: any) => void) => {
    if (message.type === "PROCESS_USER_COMMAND") {
      const transcript = message.payload?.transcript?.toLowerCase() || "";
      console.log("Background received command:", transcript);

      (async () => {
        try {
          let match;
          if (COMMANDS.OPEN_NEW_TAB.test(transcript)) {
            chrome.tabs.create({});
            sendResponse({ type: "STATUS_UPDATE", payload: { message: "Opened new tab." } });
          } else if (COMMANDS.CLOSE_CURRENT_TAB.test(transcript)) {
            const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (currentTab && currentTab.id) {
              chrome.tabs.remove(currentTab.id);
              sendResponse({ type: "STATUS_UPDATE", payload: { message: "Closed current tab." } });
            } else {
              throw new Error("No active tab to close.");
            }
          } else if ((match = COMMANDS.GO_TO_WEBSITE.exec(transcript)) !== null) {
            const siteQuery = match[1].trim();
            sendPopupMessage({ type: "STATUS_UPDATE", payload: { message: `Looking up "${siteQuery}"...` }});
            
            let urlToNavigate = siteQuery;
            if (!siteQuery.startsWith('http://') && !siteQuery.startsWith('https://')) {
              if (siteQuery.includes('.') && !siteQuery.includes(' ')) { // Simple heuristic for domain-like strings
                   urlToNavigate = `https://${siteQuery}`;
              }
            }

            if (isValidUrl(urlToNavigate)) {
                const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
                if (currentTab && currentTab.id && currentTab.url !== urlToNavigate) { // Avoid navigating to the same URL
                    chrome.tabs.update(currentTab.id, { url: urlToNavigate });
                } else if (!currentTab || currentTab.url !== urlToNavigate) {
                    chrome.tabs.create({ url: urlToNavigate });
                }
                sendResponse({ type: "STATUS_UPDATE", payload: { message: `Navigating to ${urlToNavigate}` } });
                return; 
            }

            const foundUrl = await findWebsiteUrl(siteQuery);
            if (foundUrl && isValidUrl(foundUrl)) {
              const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
              if (currentTab && currentTab.id && currentTab.url !== foundUrl) {
                chrome.tabs.update(currentTab.id, { url: foundUrl });
              } else if(!currentTab || currentTab.url !== foundUrl) {
                chrome.tabs.create({ url: foundUrl });
              }
              sendResponse({ type: "STATUS_UPDATE", payload: { message: `Navigating to ${foundUrl}` } });
            } else {
              throw new Error(`Could not find a valid URL for "${siteQuery}".`);
            }
          } else if ((match = COMMANDS.SWITCH_TAB.exec(transcript)) !== null) {
            const direction = match[1];
            const tabs = await chrome.tabs.query({ currentWindow: true });
            if (tabs.length <= 1) {
               sendResponse({ type: "STATUS_UPDATE", payload: { message: "Not enough tabs to switch." } });
               return;
            }
            const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (!activeTab) throw new Error("No active tab found.");

            let newIndex = activeTab.index;
            if (direction === 'next') {
              newIndex = (activeTab.index + 1) % tabs.length;
            } else if (direction === 'previous') {
              newIndex = (activeTab.index - 1 + tabs.length) % tabs.length;
            }
            if (tabs[newIndex] && tabs[newIndex].id) {
              chrome.tabs.update(tabs[newIndex].id!, { active: true });
              sendResponse({ type: "STATUS_UPDATE", payload: { message: `Switched to ${direction} tab.` } });
            }
          } else if (COMMANDS.RELOAD_PAGE.test(transcript)) {
            const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (currentTab && currentTab.id) {
              chrome.tabs.reload(currentTab.id);
              sendResponse({ type: "STATUS_UPDATE", payload: { message: "Page reloaded." } });
            } else {
               throw new Error("No active tab to reload.");
            }
          } else if (COMMANDS.GO_BACK.test(transcript)) {
            const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (currentTab && currentTab.id) {
              chrome.tabs.goBack(currentTab.id, () => {
                if (chrome.runtime.lastError) { // Check if navigation failed (e.g., no page to go back to)
                  sendResponse({ type: "ERROR_OCCURRED", payload: { message: "Cannot go back further." } });
                } else {
                  sendResponse({ type: "STATUS_UPDATE", payload: { message: "Navigated back." } });
                }
              });
            } else {
               throw new Error("No active tab to navigate back.");
            }
          } else if (COMMANDS.GO_FORWARD.test(transcript)) {
            const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (currentTab && currentTab.id) {
              chrome.tabs.goForward(currentTab.id, () => {
                if (chrome.runtime.lastError) { // Check if navigation failed
                  sendResponse({ type: "ERROR_OCCURRED", payload: { message: "Cannot go forward further." } });
                } else {
                  sendResponse({ type: "STATUS_UPDATE", payload: { message: "Navigated forward." } });
                }
              });
            } else {
              throw new Error("No active tab to navigate forward.");
            }
          } else if ((match = COMMANDS.SEARCH_GOOGLE.exec(transcript)) !== null) {
              const searchQuery = match[1].trim();
              sendPopupMessage({ type: "STATUS_UPDATE", payload: { message: `Searching Google for "${searchQuery}"...` }});
              const result = await searchWithGoogle(searchQuery);
              
              let message = `<p class="font-semibold mb-1">Search result for "${searchQuery}":</p><p>${result.answer}</p>`;
              if (result.sources && result.sources.length > 0) {
                const sourceLinks = result.sources
                  .map((sourceItem: GroundingChunk) => sourceItem.web && sourceItem.web.uri ? `<a href="${sourceItem.web.uri}" target="_blank" rel="noopener noreferrer" class="text-sky-400 hover:underline">${sourceItem.web.title || sourceItem.web.uri}</a>` : null)
                  .filter(Boolean)
                  .join('<br/>');
                if (sourceLinks) {
                  message += `<br/><p class="text-xs text-slate-400 mt-2">Sources:</p><div class="text-xs">${sourceLinks}</div>`;
                }
              }
               sendResponse({ type: "STATUS_UPDATE", payload: { message: message, isHtml: true } });
          } else {
            throw new Error(`Unrecognized command. Try saying "help" or check examples.`);
          }
        } catch (error: any) {
          console.error("Error processing command in background:", error);
          sendResponse({ type: "ERROR_OCCURRED", payload: { message: error.message || "Unknown error." } });
        }
      })();
      return true; 
    }
    return false; 
  });
} else {
  console.error("Voice Navigator Background: chrome.runtime.onMessage is not available. Background script messaging will not work.");
}


// Note on API_KEY:
// The geminiService.ts strictly uses process.env.API_KEY.
// The following block is a conceptual placeholder for how an extension *might* manage
// an API key if it were user-provided via an options page, but it does NOT override
// the direct usage of process.env.API_KEY in geminiService.ts.
// For this application to work, process.env.API_KEY MUST be set in the build/execution environment.
if (typeof process === 'undefined' || !process.env || !process.env.API_KEY) {
  if (chrome && chrome.storage && chrome.storage.local) {
    chrome.storage.local.get('gemini_api_key', (result: { [key: string]: any }) => {
      if (result.gemini_api_key) {
        console.warn(
          "Found 'gemini_api_key' in chrome.storage.local. " +
          "However, the application is hardcoded to use process.env.API_KEY. " +
          "This stored key is NOT being used by geminiService.ts."
        );
      } else {
        console.warn(
          "Gemini API Key (process.env.API_KEY) is not set. " +
          "Also, no 'gemini_api_key' found in chrome.storage.local. " +
          "Gemini API features will fail."
        );
      }
    });
  } else {
     console.warn(
        "Gemini API Key (process.env.API_KEY) is not set, and chrome.storage.local is not available to check for alternatives. " +
        "Gemini API features will likely fail."
      );
  }
}
