# Microphone Permission Setup for Voice Navigator

## If you're getting "not-allowed" or permission denied errors:

### Method 1: Browser Address Bar Permission
1. **Click the extension icon** to open the popup
2. **Click the microphone button** in the popup
3. **Look for a microphone icon** in your browser's address bar (usually on the right side)
4. **Click the microphone icon** in the address bar
5. **Select "Allow"** for microphone access
6. **Reload the extension** by going to `chrome://extensions/` and clicking the refresh button
7. **Try again**

### Method 2: Chrome Site Settings
1. Open Chrome and go to `chrome://settings/content/microphone`
2. Make sure "Ask before accessing (recommended)" is selected
3. Check if your extension is in the "Block" list - if so, remove it
4. Reload the extension and try again

### Method 3: Extension Permissions
1. Go to `chrome://extensions/`
2. Find "Voice Navigator" extension
3. Click "Details"
4. Make sure all permissions are enabled
5. Try reloading the extension

### Method 4: Browser Restart
Sometimes Chrome needs a restart to properly handle microphone permissions:
1. Close all Chrome windows
2. Restart Chrome
3. Go to `chrome://extensions/`
4. Reload the Voice Navigator extension
5. Try using the microphone again

## Troubleshooting Tips:

- **Make sure your microphone is connected** and working in other applications
- **Check if other websites can access your microphone** (try Google Voice Search)
- **Disable other extensions temporarily** to see if there's a conflict
- **Try in an incognito window** to test without other extensions

## Common Error Messages:

- **"not-allowed"**: Microphone permission was denied
- **"no-speech"**: No speech was detected (speak louder/clearer)
- **"audio-capture"**: Microphone hardware issue
- **"network"**: Internet connection problem

## Still Having Issues?

If none of the above work:
1. Check Chrome version (should be recent)
2. Try a different microphone if available
3. Check Windows microphone privacy settings
4. Restart your computer

The extension uses the standard Web Speech API, so if other voice-enabled websites work, this should too!
