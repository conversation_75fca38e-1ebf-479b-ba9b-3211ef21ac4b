export const GEMINI_MODEL_NAME = "gemini-2.5-flash-preview-04-17";
export const GEMINI_URL_SYSTEM_INSTRUCTION = `You are an AI assistant that helps find official website URLs. Given a company or website name, provide only its primary official URL. Do not add any explanatory text, just the URL. For example, if asked for 'Google', respond with 'https://www.google.com'. If you are unsure or cannot find a definitive official URL, respond with 'URL_NOT_FOUND'.`;
export const COMMANDS = {
    OPEN_NEW_TAB: /^open new tab$/i,
    CLOSE_CURRENT_TAB: /^close (?:current )?tab$/i,
    GO_TO_WEBSITE: /^(?:go to|navigate to|open) (.+)$/i,
    SWITCH_TAB: /^switch to (next|previous) tab$/i,
    RELOAD_PAGE: /^reload(?: page)?$/i,
    GO_BACK: /^go back$/i,
    GO_FORWARD: /^go forward$/i,
    SEARCH_GOOGLE: /^(?:search google for|google search|google) (.+)$/i,
};
