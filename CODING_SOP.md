
# Coding Standard Operating Procedure (SOP): Voice Navigator

## 1. General Principles
-   **Clarity & Readability:** Write code that is easy to understand and maintain. Prioritize clarity over overly clever solutions.
-   **Simplicity (KISS):** Keep It Simple, Stupid. Avoid unnecessary complexity.
-   **Consistency:** Adhere to consistent coding styles, naming conventions, and architectural patterns throughout the project.
-   **Modularity:** Break down code into smaller, reusable, and well-defined modules/components/functions.
-   **DRY (Don't Repeat Yourself):** Avoid redundant code. Use functions, components, and constants to promote reuse.

## 2. TypeScript Best Practices
-   **Strong Typing:** Utilize TypeScript's static typing features. Define explicit types for variables, function parameters, and return values.
-   **Interfaces & Types:** Use `interface` for object shapes and public APIs, `type` for unions, intersections, or more complex type definitions. Store shared types in `types.ts`.
-   **`unknown` vs `any`:** Prefer `unknown` over `any` when the type is truly unknown and perform type checking before use. Avoid `any` where possible.
-   **Non-null Assertion Operator (`!`):** Use sparingly and only when you are certain that a value is not null or undefined.
-   **Readonly Properties:** Use `readonly` for properties that should not be modified after an object is created.
-   **Enums:** Use standard `enum` declarations. Do not use `const enum`.
-   **Import Order:** Group imports (e.g., external libraries, internal modules, types). Place all imports at the top of the file. Use named imports.

## 3. React Best Practices
-   **Functional Components & Hooks:** Exclusively use functional components with React Hooks (`useState`, `useEffect`, `useContext`, `useCallback`, `useMemo`, `useReducer`).
-   **Component Naming:** Use PascalCase for component names (e.g., `MyComponent.tsx`).
-   **Props:**
    -   Define prop types explicitly using interfaces.
    -   Use destructuring for props: `const MyComponent = ({ prop1, prop2 }: MyProps) => { ... };`.
-   **State Management:**
    -   Use `useState` for simple local component state.
    -   Use `useReducer` for more complex state logic within a component.
    -   For communication between popup and background, use `chrome.runtime.sendMessage` and `chrome.storage.local`.
-   **`useEffect` Hook:**
    -   Be mindful of the dependency array to prevent infinite loops or stale closures.
    -   Provide an empty array `[]` for effects that should run only once on mount and clean up on unmount. Add `// eslint-disable-next-line react-hooks/exhaustive-deps` if necessary for one-time fetches.
    -   Clean up resources (event listeners, timers, subscriptions) in the return function of `useEffect`.
-   **`useCallback` & `useMemo`:** Use to optimize performance by memoizing functions and values, especially when passing them as props to memoized child components or as dependencies to `useEffect`. Be cautious of premature optimization.
-   **Keys in Lists:** Always provide a unique and stable `key` prop when rendering lists of elements.
-   **Accessibility (a11y):**
    -   Use semantic HTML elements.
    -   Ensure sufficient color contrast.
    -   Provide ARIA attributes where necessary.
-   **Component Scope:** Define helper components outside the main component function body to prevent re-rendering issues and state loss.

## 4. Tailwind CSS Best Practices
-   **Utility-First:** Embrace the utility-first approach. Compose styles using Tailwind's utility classes directly in the JSX.
-   **Readability:** Group related utility classes for better readability (e.g., layout, typography, colors).
-   **Responsiveness:** Use Tailwind's responsive prefixes (e.g., `sm:`, `md:`, `lg:`) for adaptive UIs.
-   **States:** Use state prefixes (e.g., `hover:`, `focus:`, `disabled:`) for interactive styling.
-   **No Custom CSS/Inline Styles:** Adhere strictly to using only Tailwind utility classes. Do not write separate CSS files or use inline `style` attributes.
-   **Component Abstraction:** For highly repeated patterns of utilities, encapsulate them within React components rather than creating custom CSS classes.

## 5. Chrome Extension Specifics (Manifest V3)
-   **Service Worker (`background.ts`):**
    -   Understand that service workers are event-driven and can be terminated when idle.
    -   Avoid global state that relies on continuous script execution. Use `chrome.storage` for persistent state.
    -   All Chrome API calls that manipulate the browser (tabs, etc.) should ideally be in the service worker.
-   **Message Passing:** Use `chrome.runtime.sendMessage` and `chrome.runtime.onMessage` for communication between the popup and the service worker. Define clear message types/interfaces.
-   **Permissions:** Request minimal permissions necessary for functionality in `manifest.json`.
-   **Security:** Be mindful of security implications, especially when dealing with API keys or user data.

## 6. Gemini API Usage (`@google/genai`)
-   **Initialization:** Use `new GoogleGenAI({ apiKey: process.env.API_KEY })`.
-   **Model Selection:** Use `'gemini-2.5-flash-preview-04-17'` for text tasks.
-   **API Calls:** Use `ai.models.generateContent` for single-turn queries.
-   **Response Handling:** Access text via `response.text`. If `responseMimeType: "application/json"` is used, parse `response.text` and handle potential markdown fences.
-   **System Instructions:** Utilize system instructions effectively to guide model output, especially for specific formats like URLs.
-   **Error Handling:** Implement robust error handling for API requests (network errors, status codes).
-   **API Key Management:** Assume `process.env.API_KEY` is securely provided in the execution environment of the service worker. Do not embed keys directly in client-side code committed to repositories.

## 7. Code Structure & Organization
-   **File Naming:** Use PascalCase for React components (`MyComponent.tsx`) and camelCase for other TS files (`geminiService.ts`).
-   **Directory Structure:** Follow the agreed-upon structure:
    -   `components/` for reusable React components.
    -   `services/` for API interaction logic, voice processing, etc.
    -   `types.ts` for shared TypeScript types.
    -   `constants.ts` for application-wide constants.
-   **Imports:** Keep imports clean and organized at the top of files.

## 8. Asynchronous Operations
-   **Promises & Async/Await:** Use `async/await` for cleaner asynchronous code.
-   **Error Handling:** Always include `try...catch` blocks for `async` functions that can throw errors (e.g., API calls, Chrome API interactions).

## 9. Comments & Documentation
-   **Code Comments:** Write comments to explain complex logic, assumptions, or non-obvious code sections. Avoid over-commenting simple code.
-   **JSDoc/TSDoc:** Use for documenting functions, parameters, and return types, especially for services and public APIs of components.
-   **README:** Maintain a `README.md` with project setup, build, and usage instructions (outside this SOP's scope but good practice).

## 10. Linting & Formatting
-   (Assumed) Use linters like ESLint and formatters like Prettier with appropriate configurations for TypeScript and React to enforce code style and catch common errors.

By adhering to these standards, we aim to produce a high-quality, maintainable, and robust Voice Navigator Chrome Extension.
