
# Technical Architecture: Voice Navigator Chrome Extension

## 1. Overview
Voice Navigator is a Chrome extension built using React, TypeScript, and Tailwind CSS. It utilizes the browser's SpeechRecognition API for voice input and the Google Gemini API for advanced command processing, such as resolving website names to URLs. The extension follows Manifest V3 guidelines.

## 2. Components

### 2.1. Manifest (`manifest.json`)
-   **Version:** Manifest V3
-   **Key Declarations:**
    -   `name`, `version`, `description`
    -   `action`: Defines the popup (`index.html`) and icons.
    -   `permissions`: `tabs` (to manage tabs), `storage` (for potential future settings). Microphone permission is implicitly requested by the SpeechRecognition API. `scripting` might be added if more complex page interactions are needed.
    -   `background`: Specifies the service worker (`background.js`).
    -   `icons`: Standard extension icons.

### 2.2. Popup UI (`index.html`, `index.tsx`, `App.tsx`, `components/`)
-   **Framework:** React 18+ with TypeScript.
-   **Styling:** Tailwind CSS (via CDN).
-   **Functionality:**
    -   Provides the primary user interface when the extension icon is clicked.
    -   Displays a button to initiate/stop voice listening.
    -   Shows visual feedback: listening status, transcribed text (optional), processing messages, error messages.
    -   Communicates with the background service worker for command execution.
-   **Key React Components:**
    -   `App.tsx`: Root component for the popup.
    -   `Popup.tsx`: Main UI logic, state management for the popup.
    -   Reusable UI elements (e.g., `MicIcon.tsx`, `StatusDisplay.tsx`).

### 2.3. Background Service Worker (`background.ts`)
-   **Environment:** Chrome Manifest V3 Service Worker.
-   **Responsibilities:**
    -   Persistent logic handler.
    -   Receives commands from the popup UI via `chrome.runtime.sendMessage`.
    -   Parses commands (delegating to `commandService.ts`).
    -   Interacts with Chrome APIs (`chrome.tabs`, etc.) to execute commands.
    -   Manages calls to `geminiService.ts` for URL resolution.
    -   Handles API key for Gemini securely (assumed to be available via `process.env.API_KEY` in its execution context).
    -   Can send status updates back to the popup.

### 2.4. Voice Service (`services/voiceService.ts`)
-   **Technology:** Browser's `SpeechRecognition` API (`window.webkitSpeechRecognition`).
-   **Functionality:**
    -   Manages starting and stopping voice recognition.
    -   Handles `onresult`, `onerror`, `onstart`, `onend` events from the API.
    -   Transcribes speech to text.
    -   Passes transcribed text to the popup for further processing.
    -   This service will be instantiated and used within the Popup's context.

### 2.5. Command Service (`services/commandService.ts`)
-   **Functionality:**
    -   Contains logic to parse transcribed text into actionable commands.
    -   Uses regular expressions or string matching to identify known commands (e.g., "open new tab", "close tab").
    -   Extracts parameters from commands (e.g., website name from "go to [website name]").
    -   Determines if a "go to" command involves a direct URL or a name requiring Gemini lookup.
    -   This logic will primarily be used by the background service worker.

### 2.6. Gemini Service (`services/geminiService.ts`)
-   **Technology:** `@google/genai` SDK.
-   **Functionality:**
    -   Initializes the `GoogleGenAI` client with `process.env.API_KEY`.
    -   Provides a function to take a website name (e.g., "Google") and query the Gemini API (`gemini-2.5-flash-preview-04-17` model) to find its official URL.
    -   Uses a system instruction to request only the URL in the response.
    -   Handles API responses and extracts the URL.
    -   Includes error handling for API calls.
    -   This service will be used by the background service worker.

## 3. Data Flow & Interactions

### 3.1. Voice Command Initiation:
1.  User clicks the extension icon, `popup.html` (with `App.tsx`) loads.
2.  User clicks "Start Listening" button in `Popup.tsx`.
3.  `Popup.tsx` calls `voiceService.startListening()`.
4.  `voiceService` uses `SpeechRecognition` API; browser prompts for microphone permission if not already granted.
5.  Visual feedback in `Popup.tsx` changes to "Listening...".

### 3.2. Command Processing ("Go to [website name]"):
1.  User speaks: "Go to ExampleCorp".
2.  `voiceService` transcribes it to text: "go to examplecorp".
3.  `voiceService` passes text to `Popup.tsx`.
4.  `Popup.tsx` displays "Processing: go to examplecorp..." and sends a message to `background.ts`:
    `{ type: "PROCESS_USER_COMMAND", transcript: "go to examplecorp" }`.
5.  `background.ts` (service worker) receives the message.
6.  `commandService` logic within `background.ts` parses the transcript.
    -   Identifies command: "navigate".
    -   Identifies query: "ExampleCorp".
    -   Determines "ExampleCorp" is not a direct URL, needs lookup.
7.  `background.ts` calls `geminiService.findWebsiteUrl("ExampleCorp")`.
8.  `geminiService` makes an API call to Gemini with appropriate prompt and system instruction.
9.  Gemini API returns the URL (e.g., "https://www.example.com").
10. `geminiService` extracts the URL.
11. `background.ts` receives the URL. It then uses `chrome.tabs.update()` or `chrome.tabs.create()` to navigate.
12. `background.ts` can send a status update message back to `Popup.tsx` (e.g., `{ type: "NAVIGATION_SUCCESS", url: "https://www.example.com" }`).
13. `Popup.tsx` updates UI with status: "Navigated to https://www.example.com".

### 3.3. Simple Commands ("Open new tab"):
1.  Steps 1-4 from 3.2, but user says "Open new tab".
2.  `Popup.tsx` sends message to `background.ts`: `{ type: "PROCESS_USER_COMMAND", transcript: "open new tab" }`.
3.  `background.ts` receives message.
4.  `commandService` logic parses "open new tab".
5.  `background.ts` calls `chrome.tabs.create({})`.
6.  `background.ts` can send status update to `Popup.tsx`.

## 4. Technologies Used
-   **Frontend:** React 18+, TypeScript
-   **Styling:** Tailwind CSS (CDN)
-   **Voice Recognition:** Web Speech API (`SpeechRecognition`)
-   **AI for URL Resolution:** Google Gemini API (`@google/genai` SDK, `gemini-2.5-flash-preview-04-17` model)
-   **Browser Extension Framework:** Chrome Manifest V3
-   **Icons:** SVG icons (e.g. from Lucide or custom)

## 5. Security Considerations
-   **API Key:** The Gemini API key (`process.env.API_KEY`) is assumed to be securely managed and injected into the service worker's environment. It should not be hardcoded directly in client-visible code if avoidable (build-time replacement is typical).
-   **Permissions:** Request only necessary Chrome permissions.
-   **Data Privacy:** Clearly inform users about microphone usage. Transcribed text is sent to Gemini for URL resolution commands. Avoid logging sensitive information.

## 6. Scalability & Maintainability
-   **Modular Design:** Services (`voiceService`, `geminiService`, `commandService`) promote separation of concerns.
-   **TypeScript:** Strong typing enhances code quality and maintainability.
-   **React Components:** Reusable UI components.
-   **Clear Communication:** Well-defined message passing between popup and background script.

## 7. Error Handling
-   Robust error handling for `SpeechRecognition` API (e.g., `no-speech`, `network`).
-   Error handling for Gemini API calls (e.g., network errors, invalid key, rate limits).
-   Graceful degradation if an API is unavailable.
-   User-friendly error messages displayed in the popup.
