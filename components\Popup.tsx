// This declares the 'chrome' extension API global for TypeScript
// In a full project, you would install @types/chrome instead.
declare const chrome: any;

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { ListeningState, ChromeMessage } from '../types';
import { MicIcon, ProcessingIcon, AlertTriangleIcon, LinkIcon } from './Icons';
import { useSpeechRecognition } from '../services/voiceService';

export const Popup: React.FC = () => {
  const [actualListeningState, _setActualListeningState] = useState<ListeningState>(ListeningState.IDLE);
  const listeningStateRef = useRef(actualListeningState);
  
  const [statusMessage, setStatusMessage] = useState<string>("Click the mic to start");
  const [transcript, setTranscript] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isHtmlMessage, setIsHtmlMessage] = useState<boolean>(false);

  const setListeningState = useCallback((newStateOrFn: React.SetStateAction<ListeningState>) => {
    _setActualListeningState(prevState => {
      const newState = typeof newStateOrFn === 'function' 
        ? (newStateOrFn as (prevState: ListeningState) => ListeningState)(prevState) 
        : newStateOrFn;
      listeningStateRef.current = newState;
      return newState;
    });
  }, []);


  const handleSpeechResult = useCallback((text: string) => {
    setTranscript(text);
    setListeningState(ListeningState.PROCESSING);
    setStatusMessage(`Processing: "${text}"`);
    setErrorMessage(null);
    setIsHtmlMessage(false);

    if (chrome && chrome.runtime && chrome.runtime.sendMessage) {
      chrome.runtime.sendMessage({ type: "PROCESS_USER_COMMAND", payload: { transcript: text } }, (response: ChromeMessage | undefined) => {
        if (chrome.runtime.lastError) {
          console.error("Error sending message:", chrome.runtime.lastError.message);
          setListeningState(ListeningState.ERROR);
          setStatusMessage("Error communicating with background service.");
          setErrorMessage(chrome.runtime.lastError.message || "Unknown error occurred.");
          return;
        }

        if (response) {
          if (response.type === "STATUS_UPDATE") {
            setListeningState(ListeningState.SUCCESS);
            setStatusMessage(response.payload.message);
            setIsHtmlMessage(response.payload.isHtml || false);
            setTimeout(() => {
               if (listeningStateRef.current !== ListeningState.LISTENING) { 
                  setListeningState(ListeningState.IDLE);
                  setStatusMessage("Click the mic to start");
                  setIsHtmlMessage(false);
               }
            }, 5000);
          } else if (response.type === "ERROR_OCCURRED") {
            setListeningState(ListeningState.ERROR);
            setStatusMessage("Error processing command.");
            setErrorMessage(response.payload.message);
            setIsHtmlMessage(false);
          }
        } else {
          setListeningState(ListeningState.ERROR);
          setStatusMessage("No response from background service.");
          setErrorMessage("The command might have failed silently or the background service is unresponsive.");
          setIsHtmlMessage(false);
        }
      });
    } else {
      console.error("Cannot send command: chrome.runtime.sendMessage not available in popup.");
      setListeningState(ListeningState.ERROR);
      setStatusMessage("Error: Extension communication API unavailable.");
      setErrorMessage("Unable to communicate with the background service. Ensure the extension is loaded correctly.");
      setIsHtmlMessage(false);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setListeningState, setTranscript, setStatusMessage, setErrorMessage, setIsHtmlMessage]);

  const handleSpeechError = useCallback((error: string) => {
    setListeningState(ListeningState.ERROR);
    setStatusMessage("Voice recognition error.");
    setErrorMessage(error);
    setIsHtmlMessage(false);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setListeningState, setStatusMessage, setErrorMessage, setIsHtmlMessage]); 

  const handleRecognitionStart = useCallback(() => {
    setListeningState(ListeningState.LISTENING);
    setStatusMessage("Listening...");
    setTranscript("");
    setErrorMessage(null);
    setIsHtmlMessage(false);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setListeningState, setStatusMessage, setTranscript, setErrorMessage, setIsHtmlMessage]); 

  const handleRecognitionEnd = useCallback(() => {
    if (listeningStateRef.current === ListeningState.LISTENING) {
       setListeningState(ListeningState.IDLE);
       setStatusMessage("Click the mic to start");
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setListeningState, setStatusMessage]); 

  const { isListening, startListening, stopListening } = useSpeechRecognition(
    handleSpeechResult,
    handleSpeechError,
    handleRecognitionStart,
    handleRecognitionEnd
  );

  const requestMicrophonePermission = async () => {
    console.log("Requesting microphone permission...");

    // Check if getUserMedia is available
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      console.error("getUserMedia not supported");
      setErrorMessage("Microphone access not supported in this browser.");
      setListeningState(ListeningState.ERROR);
      setStatusMessage("Browser not supported");
      return false;
    }

    try {
      console.log("Calling getUserMedia...");
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      console.log("Microphone access granted", stream);
      // Stop the stream immediately as we only needed permission
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch (error: any) {
      console.error("Microphone access error:", error);
      if (error.name === 'NotAllowedError') {
        setErrorMessage("Microphone access denied. Please allow microphone permission and try again.");
      } else if (error.name === 'NotFoundError') {
        setErrorMessage("No microphone found. Please connect a microphone and try again.");
      } else {
        setErrorMessage("Microphone access failed: " + error.message);
      }
      setListeningState(ListeningState.ERROR);
      setStatusMessage("Microphone permission required");
      return false;
    }
  };

  const toggleListening = async () => {
    console.log("Toggle listening clicked, isListening:", isListening);
    if (isListening) {
      console.log("Stopping listening...");
      stopListening();
    } else {
      console.log("Starting listening process...");
      // First request microphone permission explicitly
      const hasPermission = await requestMicrophonePermission();
      console.log("Permission result:", hasPermission);
      if (hasPermission) {
        console.log("Starting speech recognition...");
        startListening();
      }
    }
  };
  
  useEffect(() => {
    if (chrome && chrome.runtime && chrome.runtime.onMessage) {
      const messageListener = (message: ChromeMessage, sender: any, sendResponse: (response?: any) => void) => {
        if (message.type === "STATUS_UPDATE") {
          setListeningState(ListeningState.SUCCESS); 
          setStatusMessage(message.payload.message);
          setIsHtmlMessage(message.payload.isHtml || false);
          setErrorMessage(null);
        } else if (message.type === "ERROR_OCCURRED") {
          setListeningState(ListeningState.ERROR);
          setStatusMessage("Error from background:");
          setErrorMessage(message.payload.message);
          setIsHtmlMessage(false);
        }
        return false; 
      };

      chrome.runtime.onMessage.addListener(messageListener);
      return () => {
        if (chrome && chrome.runtime && chrome.runtime.onMessage) { // Check again before removing
           chrome.runtime.onMessage.removeListener(messageListener);
        }
      };
    } else {
      console.error("Voice Navigator Popup: chrome.runtime.onMessage is not available. Popup will not receive updates from background.");
      // Detailed diagnostics
      if (typeof chrome === 'undefined') {
        console.log("Debug details: The 'chrome' object itself is undefined. Ensure the extension is loaded correctly and not e.g. as a file:/// URL.");
      } else {
        console.log("Debug details: 'chrome' object is available:", chrome);
        if (typeof chrome.runtime === 'undefined') {
          console.log("Debug details: 'chrome.runtime' is undefined. This is highly unusual for an extension popup.");
        } else {
          console.log("Debug details: 'chrome.runtime' object is available:", chrome.runtime);
          if (typeof chrome.runtime.onMessage === 'undefined') {
            console.log("Debug details: 'chrome.runtime.onMessage' is undefined. Background script communication will fail.");
          }
        }
      }
      setErrorMessage("Extension API (chrome.runtime.onMessage) for receiving messages is unavailable. Ensure the extension is loaded correctly (e.g., not as a 'file:///'). See console for more details.");
      setStatusMessage("Error: Extension communication failed.");
      setListeningState(ListeningState.ERROR);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setListeningState, setStatusMessage, setErrorMessage, setIsHtmlMessage]); // Stable setters


  let micButtonColor = "bg-blue-500 hover:bg-blue-600 shadow-lg";
  if (actualListeningState === ListeningState.LISTENING) micButtonColor = "bg-red-500 hover:bg-red-600 animate-pulse shadow-lg";
  if (actualListeningState === ListeningState.PROCESSING) micButtonColor = "bg-orange-500 hover:bg-orange-600 shadow-lg";


  return (
    <div className="flex flex-col items-center justify-center p-6 space-y-4 min-h-[380px] bg-white text-gray-800 rounded-lg shadow-xl w-full border border-gray-200">
      <h1 className="text-2xl font-bold text-blue-600">Voice Navigator</h1>
      
      <button
        onClick={toggleListening}
        className={`p-6 rounded-full transition-all duration-200 ease-in-out focus:outline-none focus:ring-4 focus:ring-blue-300 transform hover:scale-105 ${micButtonColor}`}
        aria-label={isListening ? "Stop listening" : "Start listening"}
        disabled={actualListeningState === ListeningState.ERROR && errorMessage?.includes("Extension API")} // Disable mic if core API is missing
      >
        <MicIcon className="w-12 h-12 text-white" />
      </button>

      <div className="text-center min-h-[6rem] flex flex-col justify-center items-center w-full px-2">
        {actualListeningState === ListeningState.PROCESSING && (
          <ProcessingIcon className="w-7 h-7 text-orange-500 animate-spin mb-2" />
        )}
        {isHtmlMessage ? (
          <div className="text-sm font-medium text-gray-700 overflow-y-auto max-h-24 w-full prose prose-sm" dangerouslySetInnerHTML={{ __html: statusMessage }} />
        ) : (
          <p className="text-md font-medium text-gray-700">{statusMessage}</p>
        )}
        {transcript && actualListeningState !== ListeningState.LISTENING && actualListeningState !== ListeningState.PROCESSING && (
          <p className="text-xs text-gray-500 mt-1 italic">Heard: "{transcript}"</p>
        )}
      </div>

      {errorMessage && (
        <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-md text-red-800 w-full max-w-sm text-xs">
          <div className="flex items-center">
            <AlertTriangleIcon className="w-4 h-4 mr-2 text-red-600" />
            <span className="font-semibold">Error</span>
          </div>
          <p className="mt-1">{errorMessage}</p>
        </div>
      )}
      
      <div className="pt-2 text-xs text-gray-500 text-center">
        <p>Examples: "Open new tab", "Go to Google", "Reload page", "Search Google for cats"</p>
      </div>
    </div>
  );
};