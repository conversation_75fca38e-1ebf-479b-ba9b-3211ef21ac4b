// This declares the 'chrome' extension API global for TypeScript
// In a full project, you would install @types/chrome instead.
declare const chrome: any;

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { ListeningState, ChromeMessage } from '../types';
import { MicIcon, ProcessingIcon, AlertTriangleIcon, LinkIcon } from './Icons';
import { useSpeechRecognition } from '../services/voiceService';

export const Popup: React.FC = () => {
  const [actualListeningState, _setActualListeningState] = useState<ListeningState>(ListeningState.IDLE);
  const listeningStateRef = useRef(actualListeningState);

  const [statusMessage, setStatusMessage] = useState<string>("Click the mic to start");
  const [transcript, setTranscript] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isHtmlMessage, setIsHtmlMessage] = useState<boolean>(false);

  // Check Speech Recognition API availability on mount
  useEffect(() => {
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    if (!SpeechRecognition) {
      setErrorMessage("Speech Recognition API is not supported in this browser. Please use Chrome or Edge.");
      setStatusMessage("Browser not supported");
      setListeningState(ListeningState.ERROR);
    } else {
      console.log("Speech Recognition API is available");
    }
  }, []);

  const setListeningState = useCallback((newStateOrFn: React.SetStateAction<ListeningState>) => {
    _setActualListeningState(prevState => {
      const newState = typeof newStateOrFn === 'function' 
        ? (newStateOrFn as (prevState: ListeningState) => ListeningState)(prevState) 
        : newStateOrFn;
      listeningStateRef.current = newState;
      return newState;
    });
  }, []);


  const handleSpeechResult = useCallback((text: string) => {
    setTranscript(text);
    setListeningState(ListeningState.PROCESSING);
    setStatusMessage(`Processing: "${text}"`);
    setErrorMessage(null);
    setIsHtmlMessage(false);

    if (chrome && chrome.runtime && chrome.runtime.sendMessage) {
      chrome.runtime.sendMessage({ type: "PROCESS_USER_COMMAND", payload: { transcript: text } }, (response: ChromeMessage | undefined) => {
        if (chrome.runtime.lastError) {
          console.error("Error sending message:", chrome.runtime.lastError.message);
          setListeningState(ListeningState.ERROR);
          setStatusMessage("Error communicating with background service.");
          setErrorMessage(chrome.runtime.lastError.message || "Unknown error occurred.");
          return;
        }

        if (response) {
          if (response.type === "STATUS_UPDATE") {
            setListeningState(ListeningState.SUCCESS);
            setStatusMessage(response.payload.message);
            setIsHtmlMessage(response.payload.isHtml || false);
            setTimeout(() => {
               if (listeningStateRef.current !== ListeningState.LISTENING) { 
                  setListeningState(ListeningState.IDLE);
                  setStatusMessage("Click the mic to start");
                  setIsHtmlMessage(false);
               }
            }, 5000);
          } else if (response.type === "ERROR_OCCURRED") {
            setListeningState(ListeningState.ERROR);
            setStatusMessage("Error processing command.");
            setErrorMessage(response.payload.message);
            setIsHtmlMessage(false);
          }
        } else {
          setListeningState(ListeningState.ERROR);
          setStatusMessage("No response from background service.");
          setErrorMessage("The command might have failed silently or the background service is unresponsive.");
          setIsHtmlMessage(false);
        }
      });
    } else {
      console.error("Cannot send command: chrome.runtime.sendMessage not available in popup.");
      setListeningState(ListeningState.ERROR);
      setStatusMessage("Error: Extension communication API unavailable.");
      setErrorMessage("Unable to communicate with the background service. Ensure the extension is loaded correctly.");
      setIsHtmlMessage(false);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setListeningState, setTranscript, setStatusMessage, setErrorMessage, setIsHtmlMessage]);

  const handleSpeechError = useCallback((error: string) => {
    setListeningState(ListeningState.ERROR);
    setStatusMessage("Voice recognition error.");
    setErrorMessage(error);
    setIsHtmlMessage(false);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setListeningState, setStatusMessage, setErrorMessage, setIsHtmlMessage]); 

  const handleRecognitionStart = useCallback(() => {
    setListeningState(ListeningState.LISTENING);
    setStatusMessage("Listening...");
    setTranscript("");
    setErrorMessage(null);
    setIsHtmlMessage(false);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setListeningState, setStatusMessage, setTranscript, setErrorMessage, setIsHtmlMessage]); 

  const handleRecognitionEnd = useCallback(() => {
    if (listeningStateRef.current === ListeningState.LISTENING) {
       setListeningState(ListeningState.IDLE);
       setStatusMessage("Click the mic to start");
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setListeningState, setStatusMessage]); 

  const { isListening, startListening, stopListening } = useSpeechRecognition(
    handleSpeechResult,
    handleSpeechError,
    handleRecognitionStart,
    handleRecognitionEnd
  );

  const toggleListening = () => {
    console.log("Toggle listening clicked, isListening:", isListening);
    if (isListening) {
      console.log("Stopping listening...");
      stopListening();
    } else {
      console.log("Starting speech recognition...");
      // Clear any previous errors
      setErrorMessage(null);
      setStatusMessage("Requesting microphone access...");
      // Let the Speech Recognition API handle the permission request
      startListening();
    }
  };
  
  useEffect(() => {
    if (chrome && chrome.runtime && chrome.runtime.onMessage) {
      const messageListener = (message: ChromeMessage, sender: any, sendResponse: (response?: any) => void) => {
        if (message.type === "STATUS_UPDATE") {
          setListeningState(ListeningState.SUCCESS); 
          setStatusMessage(message.payload.message);
          setIsHtmlMessage(message.payload.isHtml || false);
          setErrorMessage(null);
        } else if (message.type === "ERROR_OCCURRED") {
          setListeningState(ListeningState.ERROR);
          setStatusMessage("Error from background:");
          setErrorMessage(message.payload.message);
          setIsHtmlMessage(false);
        }
        return false; 
      };

      chrome.runtime.onMessage.addListener(messageListener);
      return () => {
        if (chrome && chrome.runtime && chrome.runtime.onMessage) { // Check again before removing
           chrome.runtime.onMessage.removeListener(messageListener);
        }
      };
    } else {
      console.error("Voice Navigator Popup: chrome.runtime.onMessage is not available. Popup will not receive updates from background.");
      // Detailed diagnostics
      if (typeof chrome === 'undefined') {
        console.log("Debug details: The 'chrome' object itself is undefined. Ensure the extension is loaded correctly and not e.g. as a file:/// URL.");
      } else {
        console.log("Debug details: 'chrome' object is available:", chrome);
        if (typeof chrome.runtime === 'undefined') {
          console.log("Debug details: 'chrome.runtime' is undefined. This is highly unusual for an extension popup.");
        } else {
          console.log("Debug details: 'chrome.runtime' object is available:", chrome.runtime);
          if (typeof chrome.runtime.onMessage === 'undefined') {
            console.log("Debug details: 'chrome.runtime.onMessage' is undefined. Background script communication will fail.");
          }
        }
      }
      setErrorMessage("Extension API (chrome.runtime.onMessage) for receiving messages is unavailable. Ensure the extension is loaded correctly (e.g., not as a 'file:///'). See console for more details.");
      setStatusMessage("Error: Extension communication failed.");
      setListeningState(ListeningState.ERROR);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setListeningState, setStatusMessage, setErrorMessage, setIsHtmlMessage]); // Stable setters


  let micButtonClass = "mic-button idle";
  if (actualListeningState === ListeningState.LISTENING) micButtonClass = "mic-button listening";
  if (actualListeningState === ListeningState.PROCESSING) micButtonClass = "mic-button processing";


  return (
    <div className="popup-container">
      <h1 className="title">Voice Navigator</h1>

      <button
        onClick={toggleListening}
        className={micButtonClass}
        aria-label={isListening ? "Stop listening" : "Start listening"}
        disabled={actualListeningState === ListeningState.ERROR && errorMessage?.includes("Extension API")} // Disable mic if core API is missing
      >
        <MicIcon className="mic-icon" />
      </button>

      <div className="status-area">
        {actualListeningState === ListeningState.PROCESSING && (
          <ProcessingIcon className="processing-icon mb-2" />
        )}
        {isHtmlMessage ? (
          <div className="status-message overflow-y-auto max-h-24 prose" dangerouslySetInnerHTML={{ __html: statusMessage }} />
        ) : (
          <p className="status-message">{statusMessage}</p>
        )}
        {transcript && actualListeningState !== ListeningState.LISTENING && actualListeningState !== ListeningState.PROCESSING && (
          <p className="transcript">Heard: "{transcript}"</p>
        )}
      </div>

      {errorMessage && (
        <div className="error-container">
          <div className="error-header">
            <AlertTriangleIcon className="alert-icon" />
            <span className="error-title">Error</span>
          </div>
          <p className="error-message">{errorMessage}</p>
        </div>
      )}

      <div className="examples">
        <p>Examples: "Open new tab", "Go to Google", "Reload page", "Search Google for cats"</p>
      </div>
    </div>
  );
};