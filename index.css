/* Voice Navigator Extension Styles */

/* Reset and base styles */
* {
  box-sizing: border-box;
}

body, html, #root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #ffffff;
}

body {
  width: 400px;
  min-height: 380px;
  height: auto;
  overflow: hidden;
}

#root {
  display: flex;
  flex-direction: column;
}

/* Main container */
.popup-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  gap: 16px;
  min-height: 380px;
  background-color: #ffffff;
  color: #1f2937;
  border-radius: 8px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  width: 100%;
  border: 1px solid #e5e7eb;
}

/* Title */
.title {
  font-size: 24px;
  font-weight: bold;
  color: #2563eb;
  margin: 0;
}

/* Microphone button */
.mic-button {
  padding: 24px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  outline: none;
  transform: scale(1);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.mic-button:hover {
  transform: scale(1.05);
}

.mic-button:focus {
  box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.3);
}

.mic-button.idle {
  background-color: #3b82f6;
}

.mic-button.idle:hover {
  background-color: #2563eb;
}

.mic-button.listening {
  background-color: #ef4444;
  animation: pulse 2s infinite;
}

.mic-button.listening:hover {
  background-color: #dc2626;
}

.mic-button.processing {
  background-color: #f97316;
}

.mic-button.processing:hover {
  background-color: #ea580c;
}

.mic-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: scale(1);
}

/* Icon styles */
.mic-icon {
  width: 48px;
  height: 48px;
  color: white;
}

.processing-icon {
  width: 28px;
  height: 28px;
  color: #f97316;
  animation: spin 1s linear infinite;
}

.alert-icon {
  width: 16px;
  height: 16px;
  color: #dc2626;
}

/* Status area */
.status-area {
  text-align: center;
  min-height: 96px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 0 8px;
}

.status-message {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  margin: 0;
}

.transcript {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
  font-style: italic;
}

/* Error message */
.error-container {
  margin-top: 8px;
  padding: 12px;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  color: #991b1b;
  width: 100%;
  max-width: 384px;
  font-size: 12px;
}

.error-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.error-title {
  font-weight: 600;
  margin-left: 8px;
}

.error-message {
  margin: 4px 0 0 0;
}

/* Examples */
.examples {
  padding-top: 8px;
  font-size: 12px;
  color: #6b7280;
  text-align: center;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Utility classes */
.mb-2 {
  margin-bottom: 8px;
}

.mt-1 {
  margin-top: 4px;
}

.mt-2 {
  margin-top: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.overflow-y-auto {
  overflow-y: auto;
}

.max-h-24 {
  max-height: 96px;
}

.prose {
  max-width: none;
}

.prose p {
  margin: 0;
}

.prose a {
  color: #2563eb;
  text-decoration: underline;
}

.prose a:hover {
  text-decoration: none;
}
